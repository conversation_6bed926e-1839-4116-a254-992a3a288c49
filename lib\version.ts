// Auto-generated version file - DO NOT EDIT MANUALLY
// Generated at: 2025-06-03T08:28:40.806Z

export const APP_VERSION = '1748939320774-856024e';
export const BUILD_TIMESTAMP = 1748939320806;

// Version checking utilities
export const VERSION_CHECK_INTERVAL = 30000; // Check every 30 seconds
export const VERSION_STORAGE_KEY = 'app_version';

export interface VersionInfo {
  version: string;
  timestamp: number;
  forceReload?: boolean;
}

export async function checkForUpdate(): Promise<VersionInfo | null> {
  try {
    // Fetch version info from a dedicated endpoint
    const response = await fetch(`/api/version?${new URLSearchParams({
      t: Date.now().toString() // Prevent caching
    })}`, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch version info');
    }
    
    const versionInfo: VersionInfo = await response.json();
    const currentVersion = localStorage.getItem(VERSION_STORAGE_KEY);
    
    if (currentVersion && currentVersion !== versionInfo.version) {
      return {
        ...versionInfo,
        forceReload: true
      };
    }
    
    // Store current version
    localStorage.setItem(VERSION_STORAGE_KEY, versionInfo.version);
    
    return versionInfo;
  } catch (error) {
    console.error('Version check failed:', error);
    return null;
  }
}

export function forceAppUpdate() {
  // Clear all caches
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      for (const registration of registrations) {
        registration.unregister();
      }
    });
  }
  
  // Clear localStorage/sessionStorage
  try {
    localStorage.clear();
    sessionStorage.clear();
  } catch (e) {
    console.warn('Could not clear storage:', e);
  }
  
  // Force reload with cache bust
  window.location.href = `${window.location.href}?v=${Date.now()}`;
}
