/**
 * Admin credit adjustment API endpoint
 * Allows admins to adjust user credits with audit trail
 */

import { type NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth/admin-utils";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/app/(auth)/auth-server";
import { ChatSDKError } from "@/lib/errors";

interface CreditAdjustmentRequest {
  user_id: string;
  amount: number; // Always positive - operation_type determines the action
  reason: string;
  operation_type: "add" | "deduct" | "set";
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    requireAdmin(session.user, "credit adjustment");

    const body: CreditAdjustmentRequest = await request.json();
    const { user_id, amount, reason, operation_type } = body;

    // Validate input
    if (!user_id || typeof user_id !== "string") {
      return NextResponse.json({ error: "Invalid user ID" }, { status: 400 });
    }

    if (typeof amount !== "number" || amount <= 0) {
      return NextResponse.json({ error: "Amount must be a positive number" }, { status: 400 });
    }

    if (!reason || typeof reason !== "string" || reason.trim().length < 5) {
      return NextResponse.json({ error: "Reason must be at least 5 characters" }, { status: 400 });
    }

    if (!["add", "deduct", "set"].includes(operation_type)) {
      return NextResponse.json({ error: "Invalid operation type" }, { status: 400 });
    }

    const supabase = await createClient();

    // Get current user data
    const { data: user, error: userError } = await supabase.from("User").select("id, email, credits").eq("id", user_id).single();

    if (userError || !user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Calculate new balance
    let newBalance: number;
    let actualAmount: number; // Amount to record in transaction

    switch (operation_type) {
      case "add":
        newBalance = user.credits + amount;
        actualAmount = amount;
        break;
      case "deduct":
        newBalance = Math.max(0, user.credits - amount);
        actualAmount = -Math.min(amount, user.credits); // Don't deduct more than available
        break;
      case "set":
        newBalance = amount;
        actualAmount = amount - user.credits;
        break;
      default:
        return NextResponse.json({ error: "Invalid operation type" }, { status: 400 });
    }

    // Start transaction
    const { data: updateResult, error: updateError } = await supabase
      .from("User")
      .update({ credits: newBalance })
      .eq("id", user_id)
      .select("credits")
      .single();

    if (updateError) {
      console.error("Failed to update user credits:", updateError);
      return NextResponse.json({ error: "Failed to update credits" }, { status: 500 });
    }

    // Record transaction in audit trail
    const { error: transactionError } = await supabase.from("credit_transactions").insert({
      user_id: user_id,
      amount: actualAmount,
      description: `Admin adjustment: ${reason.trim()}`,
      transaction_type: "admin_adjustment",
    });

    if (transactionError) {
      console.error("Failed to record credit transaction:", transactionError);
      // Note: We don't rollback the credit update here as it's already committed
      // In a production system, you'd want to use a proper database transaction
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        previous_credits: user.credits,
        new_credits: newBalance,
        adjustment_amount: actualAmount,
      },
      operation: {
        type: operation_type,
        amount: amount,
        reason: reason.trim(),
      },
    });
  } catch (error) {
    console.error("Admin credit adjustment error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Get credit adjustment history for a user
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    requireAdmin(session.user, "credit history access");

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("user_id");
    const limit = Number.parseInt(searchParams.get("limit") || "10", 10);
    const offset = Number.parseInt(searchParams.get("offset") || "0", 10);

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }

    const supabase = await createClient();

    // Get credit transaction history
    const { data: transactions, error: transactionsError } = await supabase
      .from("credit_transactions")
      .select(
        `
        id,
        amount,
        description,
        transaction_type,
        created_at
      `
      )
      .eq("user_id", userId)
      .eq("transaction_type", "admin_adjustment")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (transactionsError) {
      console.error("Failed to fetch credit transactions:", transactionsError);
      return NextResponse.json({ error: "Failed to fetch transaction history" }, { status: 500 });
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from("credit_transactions")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId)
      .eq("transaction_type", "admin_adjustment");

    if (countError) {
      console.error("Failed to count credit transactions:", countError);
      return NextResponse.json({ error: "Failed to count transactions" }, { status: 500 });
    }

    return NextResponse.json({
      transactions: transactions || [],
      total: count || 0,
      limit,
      offset,
      hasMore: (count || 0) > offset + limit,
    });
  } catch (error) {
    console.error("Admin credit history error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
